{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18"}, "version": {"isDirty": true, "major": 3, "minor": 18, "patch": 1, "string": "3.18.1-g262b901-dirty", "suffix": "g262b901"}}, "objects": [{"jsonFile": "codemodel-v2-5e92603c140e52ed1914.json", "kind": "codemodel", "version": {"major": 2, "minor": 1}}, {"jsonFile": "cache-v2-b877b6330d42a2db0c06.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-cd0509e82611766b913f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-b877b6330d42a2db0c06.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-cd0509e82611766b913f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5e92603c140e52ed1914.json", "kind": "codemodel", "version": {"major": 2, "minor": 1}}}}}