{"buildFiles": ["D:\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.18.1\\bin\\ninja.exe", "-C", "D:\\apnabazar\\codecanyon-mA75RCjN-listplace-business-directory-listing-flutter-app-android-ios\\listplace\\android\\app\\.cxx\\Debug\\361x196o\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.18.1\\bin\\ninja.exe", "-C", "D:\\apnabazar\\codecanyon-mA75RCjN-listplace-business-directory-listing-flutter-app-android-ios\\listplace\\android\\app\\.cxx\\Debug\\361x196o\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}