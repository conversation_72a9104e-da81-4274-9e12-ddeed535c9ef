name: listplace
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1 

environment:
  sdk: ^3.7.2 # Flutter version 3.29.2 (channel stable)

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # flutter build apk --split-per-abi
  cupertino_icons: ^1.0.2
  # state management
  get:
  # network communication
  dio: ^5.7.0
  http: 
  cached_network_image: ^3.3.1
  open_file: ^3.3.2
  photo_view: ^0.15.0
  fluttertoast: ^8.2.12
  # local storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  permission_handler: ^11.3.1
  connectivity_plus: ^5.0.2
  lecle_downloads_path_provider: ^0.0.2+8
  # to responsive ui design
  flutter_screenutil: ^5.8.4
  path_provider: ^2.1.1
  path: ^1.8.3
  lottie: ^3.1.3
  file_picker: ^8.1.4
  image_picker: ^1.0.2
  country_code_picker: ^3.0.0
  intl:
  # change_app_name and package_name
  # flutter pub run change_app_package_name:main com.new.package.name
  # flutter pub run rename_app:main all="My App Name"
  change_app_package_name:
  rename_app: ^1.3.1
  # change app icon
  flutter_launcher_icons: ^0.14.2
  flutter_svg: ^2.0.9
  dropdown_button2: ^2.3.9
  google_maps_flutter: ^2.10.1
  dotted_border: ^2.1.0
  carousel_slider: ^5.0.0
  dots_indicator: ^3.0.0
  dotted_line: ^3.2.2
  qr_flutter: ^4.1.0
  emoji_picker_flutter: ^1.6.3
  flutter_dotenv: ^5.2.1
  # debugPrint
  logger: ^2.5.0
  flutter_rating_bar: ^4.0.1
  percent_indicator: ^4.2.4
  url_launcher: 
  confetti: ^0.8.0
  timeago:
  syncfusion_flutter_datepicker: 
  google_maps_place_picker_mb: 
  day_night_time_picker:
  font_awesome_flutter: ^10.8.0
  youtube_player_flutter: ^9.1.1
  states_rebuilder: ^6.4.0
  flutter_local_notifications: ^18.0.1
  pusher_channels_flutter: ^2.0.0
  flutter_map:
  latlong2: ^0.9.1
  searchfield: ^1.2.6
  face_pile: ^0.0.2
  readmore: ^3.0.0
  share_plus: ^10.1.3
  webview_flutter: ^4.10.0
  # Payment Gateway
  flutter_stripe: 
  razorpay_flutter: ^1.4.0
  shimmer: ^3.0.0
  slide_action: ^0.0.2
  flutter_zoom_drawer: ^3.2.0
  #flutter build apk --split-per-abi --build-name=2.0.0 --build-number=2

dev_dependencies:
  flutter_test:
    sdk: flutter
#change_app_icon
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/icons/app_icon.png"
  min_sdk_android: 21
#dart run flutter_launcher_icons:main

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/
    - assets/json/
    - assets/svg/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Jost
      fonts:
        - asset: assets/fonts/Jost-Black.ttf
        - asset: assets/fonts/Jost-Bold.ttf
        - asset: assets/fonts/Jost-ExtraBold.ttf
        - asset: assets/fonts/Jost-ExtraLight.ttf
        - asset: assets/fonts/Jost-Light.ttf
        - asset: assets/fonts/Jost-Medium.ttf
        - asset: assets/fonts/Jost-Regular.ttf
        - asset: assets/fonts/Jost-SemiBold.ttf
    - family: Glory
      fonts:
        - asset: assets/fonts/Glory-Bold.ttf
        - asset: assets/fonts/Glory-ExtraBold.ttf
        - asset: assets/fonts/Glory-Medium.ttf
        - asset: assets/fonts/Glory-Regular.ttf
    - family: Goldman
      fonts:
        - asset: assets/fonts/Goldman-Bold.ttf
        - asset: assets/fonts/Goldman-Regular.ttf
  
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
